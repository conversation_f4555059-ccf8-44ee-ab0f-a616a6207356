import React, { useState } from "react"
import { UsageGuidelines } from "@/components"
import { Button, Typography } from "@apollo/ui"
import { BottomSheet, BottomSheetContent } from "@apollo/storefront"
import { <PERSON><PERSON><PERSON>, Heart, Star } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * BottomSheetContent component
 *
 * The BottomSheetContent component provides a structured layout for content within a BottomSheet.
 * It includes support for a footer area, making it ideal for forms, product details, or any
 * content that needs action buttons at the bottom.
 *
 * Notes:
 * - Designed to be used inside BottomSheet components
 * - Provides consistent spacing and layout
 * - Supports optional footer for action buttons
 * - Flexible content area for any type of content
 * - Follows Apollo storefront design patterns
 */
const meta = {
  title: "@apollo∕storefront/Components/Layout/BottomSheetContent",
  component: BottomSheetContent,
  tags: ["autodocs"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=2637-6138&m=dev",
    },
    docs: {
      description: {
        component:
          "The BottomSheetContent component provides a structured layout for content within BottomSheet components. It includes support for footer areas and consistent spacing, making it perfect for forms, product details, and interactive content.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { BottomSheetContent } from "@apollo/storefront"`} language="tsx" />
          <h2 id="bottomsheetcontent-props">Props</h2>
          <ArgTypes />
          <h2 id="bottomsheetcontent-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use BottomSheetContent to provide consistent layout within BottomSheet components",
              "Include footer for action buttons when user interaction is required",
              "Keep content organized and scannable with proper spacing",
              "Use the footer for primary and secondary actions",
              "Ensure footer buttons are appropriately sized for touch interaction",
              "Consider the content hierarchy and visual flow",
              "Test footer button placement on different screen sizes",
            ]}
          />
          <h2 id="bottomsheetcontent-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Ensure footer buttons have sufficient size and spacing for
                touch interaction (minimum 44px touch target).
              </>,
              <>
                Use clear, descriptive labels for footer action buttons that
                indicate what will happen when pressed.
              </>,
              <>
                Maintain proper focus order from content to footer actions.
              </>,
              <>
                Ensure sufficient color contrast for all content and footer
                elements.
              </>,
              <>
                Consider keyboard navigation between content and footer elements.
              </>,
              <>
                Test with screen readers to ensure content and actions are
                properly announced.
              </>,
            ]}
          />
          <h2 id="bottomsheetcontent-examples">Examples</h2>
          <Stories title="" />
        </>
      ),
    },
  },
  argTypes: {
    children: {
      control: false,
      description: "Content to be displayed in the main area of the bottom sheet content.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    footer: {
      control: false,
      description: "Footer content, typically action buttons or additional controls.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the component.",
      table: {
        type: { summary: "string" },
      },
    },
  },
  args: {
    children: "Content goes here",
  },
} satisfies Meta<typeof BottomSheetContent>

export default meta

type Story = StoryObj<typeof BottomSheetContent>

/** Default BottomSheetContent with basic content */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview BottomSheetContent with default settings. Shows basic content layout without footer.",
      },
    },
  },
  render: (args) => (
    <div style={{ width: "300px", border: "1px solid #e0e0e0", borderRadius: "8px", overflow: "hidden" }}>
      <BottomSheetContent {...args}>
        <div style={{ padding: "16px" }}>
          <Typography level="titleMedium" style={{ marginBottom: "8px" }}>
            Product Information
          </Typography>
          <Typography level="bodyMedium" style={{ marginBottom: "12px" }}>
            This is the main content area where you can display any information, forms, or interactive elements.
          </Typography>
          <Typography level="bodySmall" style={{ color: "#666" }}>
            The content area is flexible and can accommodate various types of content.
          </Typography>
        </div>
      </BottomSheetContent>
    </div>
  ),
}

/** BottomSheetContent with footer buttons */
export const WithFooter: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent with footer containing action buttons. Common pattern for forms and confirmations.",
      },
    },
  },
  render: () => (
    <div style={{ width: "300px", border: "1px solid #e0e0e0", borderRadius: "8px", overflow: "hidden" }}>
      <BottomSheetContent
        footer={
          <div style={{ display: "flex", gap: "8px" }}>
            <Button variant="outline" style={{ flex: 1 }}>
              Cancel
            </Button>
            <Button style={{ flex: 1 }}>
              Confirm
            </Button>
          </div>
        }
      >
        <div style={{ padding: "16px" }}>
          <Typography level="titleMedium" style={{ marginBottom: "8px" }}>
            Confirm Action
          </Typography>
          <Typography level="bodyMedium">
            Are you sure you want to proceed with this action? This cannot be undone.
          </Typography>
        </div>
      </BottomSheetContent>
    </div>
  ),
}

/** BottomSheetContent with form layout */
export const FormLayout: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent configured for form input with multiple fields and action buttons.",
      },
    },
  },
  render: () => (
    <div style={{ width: "300px", border: "1px solid #e0e0e0", borderRadius: "8px", overflow: "hidden" }}>
      <BottomSheetContent
        footer={
          <div style={{ display: "flex", gap: "8px" }}>
            <Button variant="outline" style={{ flex: 1 }}>
              Cancel
            </Button>
            <Button style={{ flex: 1 }}>
              Save
            </Button>
          </div>
        }
      >
        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <div>
            <label style={{ display: "block", marginBottom: "4px", fontSize: "14px", fontWeight: "500" }}>
              Product Name
            </label>
            <input
              type="text"
              placeholder="Enter product name"
              style={{
                width: "100%",
                padding: "8px 12px",
                border: "1px solid #ccc",
                borderRadius: "4px",
                fontSize: "14px",
              }}
            />
          </div>
          <div>
            <label style={{ display: "block", marginBottom: "4px", fontSize: "14px", fontWeight: "500" }}>
              Description
            </label>
            <textarea
              placeholder="Enter description"
              rows={3}
              style={{
                width: "100%",
                padding: "8px 12px",
                border: "1px solid #ccc",
                borderRadius: "4px",
                fontSize: "14px",
                resize: "vertical",
              }}
            />
          </div>
          <div>
            <label style={{ display: "block", marginBottom: "4px", fontSize: "14px", fontWeight: "500" }}>
              Price
            </label>
            <input
              type="number"
              placeholder="0.00"
              style={{
                width: "100%",
                padding: "8px 12px",
                border: "1px solid #ccc",
                borderRadius: "4px",
                fontSize: "14px",
              }}
            />
          </div>
        </div>
      </BottomSheetContent>
    </div>
  ),
}

/** BottomSheetContent with product details layout */
export const ProductDetails: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent displaying product information with rating, price, and action buttons.",
      },
    },
  },
  render: () => (
    <div style={{ width: "300px", border: "1px solid #e0e0e0", borderRadius: "8px", overflow: "hidden" }}>
      <BottomSheetContent
        footer={
          <div style={{ display: "flex", gap: "8px" }}>
            <Button variant="outline" startDecorator={<Heart size={16} />} style={{ flex: 1 }}>
              Wishlist
            </Button>
            <Button startDecorator={<ShoppingCart size={16} />} style={{ flex: 1 }}>
              Add to Cart
            </Button>
          </div>
        }
      >
        <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
          <div>
            <Typography level="titleLarge" style={{ marginBottom: "4px" }}>
              Premium Wireless Headphones
            </Typography>
            <Typography level="bodyMedium" style={{ color: "#666", marginBottom: "8px" }}>
              High-quality audio experience with noise cancellation
            </Typography>
          </div>

          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <div style={{ display: "flex", gap: "2px" }}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  size={16}
                  style={{ color: star <= 4 ? "#ffd700" : "#ddd" }}
                />
              ))}
            </div>
            <Typography level="bodySmall" style={{ color: "#666" }}>
              4.0 (124 reviews)
            </Typography>
          </div>

          <div style={{ display: "flex", alignItems: "baseline", gap: "8px" }}>
            <Typography level="titleLarge" style={{ color: "#2d5a27", fontWeight: "bold" }}>
              ฿1,599
            </Typography>
            <Typography level="bodyMedium" style={{ color: "#999", textDecoration: "line-through" }}>
              ฿1,999
            </Typography>
            <span style={{
              background: "#ff4757",
              color: "white",
              padding: "2px 6px",
              borderRadius: "4px",
              fontSize: "12px",
              fontWeight: "bold"
            }}>
              20% OFF
            </span>
          </div>

          <div>
            <Typography level="bodySmall" style={{ fontWeight: "500", marginBottom: "4px" }}>
              Features:
            </Typography>
            <ul style={{ margin: 0, paddingLeft: "16px", fontSize: "14px", color: "#666" }}>
              <li>Active noise cancellation</li>
              <li>30-hour battery life</li>
              <li>Wireless charging case</li>
              <li>Premium build quality</li>
            </ul>
          </div>
        </div>
      </BottomSheetContent>
    </div>
  ),
}

/** BottomSheetContent with single action footer */
export const SingleAction: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent with a single primary action button in the footer.",
      },
    },
  },
  render: () => (
    <div style={{ width: "300px", border: "1px solid #e0e0e0", borderRadius: "8px", overflow: "hidden" }}>
      <BottomSheetContent
        footer={
          <Button style={{ width: "100%" }}>
            Continue
          </Button>
        }
      >
        <div style={{ padding: "16px", textAlign: "center" }}>
          <div style={{
            width: "60px",
            height: "60px",
            background: "#e8f5e8",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "0 auto 16px"
          }}>
            <span style={{ fontSize: "24px" }}>✓</span>
          </div>
          <Typography level="titleMedium" style={{ marginBottom: "8px" }}>
            Success!
          </Typography>
          <Typography level="bodyMedium" style={{ color: "#666" }}>
            Your action has been completed successfully. You can now proceed to the next step.
          </Typography>
        </div>
      </BottomSheetContent>
    </div>
  ),
}

/** Comprehensive showcase of BottomSheetContent configurations */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of different BottomSheetContent configurations and use cases.",
      },
    },
  },
  render: function StatesDemo() {
    const [openBasic, setOpenBasic] = useState(false)
    const [openForm, setOpenForm] = useState(false)
    const [openProduct, setOpenProduct] = useState(false)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px", padding: "20px" }}>
        <Typography level="headlineSmall" style={{ marginBottom: "16px" }}>
          BottomSheetContent Examples
        </Typography>

        <div style={{ display: "flex", gap: "12px", flexWrap: "wrap" }}>
          <Button onClick={() => setOpenBasic(true)}>Basic Content</Button>
          <Button onClick={() => setOpenForm(true)} variant="outline">Form Layout</Button>
          <Button onClick={() => setOpenProduct(true)} variant="text">Product Details</Button>
        </div>

        {/* Basic Content */}
        <BottomSheet
          open={openBasic}
          onClose={() => setOpenBasic(false)}
          title="Basic Content"
        >
          <BottomSheetContent
            footer={
              <Button onClick={() => setOpenBasic(false)} style={{ width: "100%" }}>
                Close
              </Button>
            }
          >
            <div style={{ padding: "16px" }}>
              <Typography level="bodyLarge">
                This is a basic BottomSheetContent example with simple content and a single action button.
              </Typography>
            </div>
          </BottomSheetContent>
        </BottomSheet>

        {/* Form Layout */}
        <BottomSheet
          open={openForm}
          onClose={() => setOpenForm(false)}
          title="Contact Form"
        >
          <BottomSheetContent
            footer={
              <div style={{ display: "flex", gap: "8px" }}>
                <Button variant="outline" onClick={() => setOpenForm(false)} style={{ flex: 1 }}>
                  Cancel
                </Button>
                <Button onClick={() => setOpenForm(false)} style={{ flex: 1 }}>
                  Submit
                </Button>
              </div>
            }
          >
            <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
              <div>
                <label style={{ display: "block", marginBottom: "4px", fontSize: "14px", fontWeight: "500" }}>
                  Name
                </label>
                <input
                  type="text"
                  placeholder="Your name"
                  style={{
                    width: "100%",
                    padding: "8px 12px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                    fontSize: "14px",
                  }}
                />
              </div>
              <div>
                <label style={{ display: "block", marginBottom: "4px", fontSize: "14px", fontWeight: "500" }}>
                  Message
                </label>
                <textarea
                  placeholder="Your message"
                  rows={3}
                  style={{
                    width: "100%",
                    padding: "8px 12px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                    fontSize: "14px",
                    resize: "vertical",
                  }}
                />
              </div>
            </div>
          </BottomSheetContent>
        </BottomSheet>

        {/* Product Details */}
        <BottomSheet
          open={openProduct}
          onClose={() => setOpenProduct(false)}
          title="Product Details"
        >
          <BottomSheetContent
            footer={
              <div style={{ display: "flex", gap: "8px" }}>
                <Button variant="outline" startDecorator={<Heart size={16} />} style={{ flex: 1 }}>
                  Wishlist
                </Button>
                <Button startDecorator={<ShoppingCart size={16} />} style={{ flex: 1 }}>
                  Add to Cart
                </Button>
              </div>
            }
          >
            <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
              <Typography level="titleMedium">Smart Watch Pro</Typography>
              <Typography level="bodyMedium" style={{ color: "#666" }}>
                Advanced fitness tracking with heart rate monitoring
              </Typography>
              <div style={{ display: "flex", alignItems: "baseline", gap: "8px" }}>
                <Typography level="titleLarge" style={{ color: "#2d5a27", fontWeight: "bold" }}>
                  ฿12,900
                </Typography>
              </div>
            </div>
          </BottomSheetContent>
        </BottomSheet>
      </div>
    )
  },
}
