import React, { useState } from "react"
import { ComponentRules, UsageGuidelines } from "@/components"
import { Button, Typography } from "@apollo/ui"
import { BottomSheet, BottomSheetContent } from "@apollo/storefront"
import { Close, Settings, Share, Heart } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * BottomSheet component
 *
 * The BottomSheet component provides a mobile-friendly modal that slides up from the bottom
 * of the screen. It's commonly used for mobile interfaces to display additional content,
 * actions, or forms without taking up the full screen.
 *
 * Notes:
 * - Built on top of Modal.Root from @apollo/ui
 * - Includes a drag handle for visual indication
 * - Supports custom titles and close icons
 * - Designed for mobile-first experiences
 * - Can contain any content including forms, lists, or actions
 */
const meta = {
  title: "@apollo∕storefront/Components/Feedback/BottomSheet",
  component: BottomSheet,
  tags: ["autodocs"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    layout: "fullscreen",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=2637-6138&m=dev",
    },
    docs: {
      description: {
        component:
          "The BottomSheet component renders a modal that slides up from the bottom of the screen. Perfect for mobile interfaces, it provides an intuitive way to display additional content, actions, or forms without overwhelming the user.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { BottomSheet } from "@apollo/storefront"`} language="tsx" />
          <h2 id="bottomsheet-props">Props</h2>
          <ArgTypes />
          <h2 id="bottomsheet-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use BottomSheet for secondary actions or content that doesn't require full screen attention",
              "Include a clear title to help users understand the purpose of the bottom sheet",
              "Provide an easy way to close the bottom sheet (close button or backdrop click)",
              "Keep content concise and focused on a single task or purpose",
              "Use the drag handle to indicate that the sheet can be dismissed",
              "Consider the content height and ensure it doesn't exceed the viewport",
              "Test on various mobile devices to ensure proper behavior",
            ]}
          />
          <h2 id="bottomsheet-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Ensure the bottom sheet has proper focus management when opened,
                moving focus to the first interactive element within the sheet.
              </>,
              <>
                Provide keyboard navigation support, including the ability to close
                the sheet with the Escape key.
              </>,
              <>
                Use descriptive titles that clearly indicate the purpose of the
                bottom sheet content.
              </>,
              <>
                Ensure sufficient color contrast for all text and interactive
                elements within the bottom sheet.
              </>,
              <>
                Test with screen readers to ensure the content is properly
                announced and navigable.
              </>,
              <>
                Consider providing alternative navigation methods for users who
                may have difficulty with touch gestures.
              </>,
            ]}
          />
          <h2 id="bottomsheet-examples">Examples</h2>
          <Stories title="" />
        </>
      ),
    },
  },
  argTypes: {
    open: {
      control: { type: "boolean" },
      description: "Controls whether the bottom sheet is visible.",
      table: {
        type: { summary: "boolean" },
      },
    },
    title: {
      control: { type: "text" },
      description: "Title displayed in the header of the bottom sheet.",
      table: {
        type: { summary: "string" },
      },
    },
    onClose: {
      control: false,
      description: "Callback function called when the bottom sheet should be closed.",
      table: {
        type: { summary: "() => void" },
      },
    },
    closeIcon: {
      control: false,
      description: "Custom close icon element. If not provided, a default close button will be shown when onClose is provided.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    snapPoints: {
      control: false,
      description: "Function to define snap points for the bottom sheet height.",
      table: {
        type: { summary: "(props: SnapPointProps) => number[] | number" },
      },
    },
    children: {
      control: false,
      description: "Content to be displayed inside the bottom sheet.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
  },
  args: {
    open: false,
    title: "Bottom Sheet",
  },
} satisfies Meta<typeof BottomSheet>

export default meta

type Story = StoryObj<typeof BottomSheet>

/** Default BottomSheet with basic content */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview BottomSheet with default settings. Shows a basic bottom sheet with title and simple content. Click the button to open the bottom sheet.",
      },
    },
  },
  render: (args) => {
    const [open, setOpen] = useState(false)
    
    return (
      <>
        <div style={{ padding: "20px" }}>
          <Button onClick={() => setOpen(true)}>Open Bottom Sheet</Button>
        </div>
        <BottomSheet
          {...args}
          open={open}
          onClose={() => setOpen(false)}
          title="Welcome"
        >
          <div style={{ padding: "16px" }}>
            <Typography level="bodyLarge">
              This is a basic bottom sheet with some content. You can put any content here.
            </Typography>
          </div>
        </BottomSheet>
      </>
    )
  },
}

/** BottomSheet with form content */
export const WithForm: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheet containing a form with input fields and action buttons.",
      },
    },
  },
  render: () => {
    const [open, setOpen] = useState(false)
    
    return (
      <>
        <div style={{ padding: "20px" }}>
          <Button onClick={() => setOpen(true)}>Open Form Bottom Sheet</Button>
        </div>
        <BottomSheet
          open={open}
          onClose={() => setOpen(false)}
          title="Contact Information"
        >
          <BottomSheetContent
            footer={
              <div style={{ display: "flex", gap: "8px" }}>
                <Button variant="outline" onClick={() => setOpen(false)} style={{ flex: 1 }}>
                  Cancel
                </Button>
                <Button onClick={() => setOpen(false)} style={{ flex: 1 }}>
                  Save
                </Button>
              </div>
            }
          >
            <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
              <div>
                <label style={{ display: "block", marginBottom: "4px", fontSize: "14px", fontWeight: "500" }}>
                  Name
                </label>
                <input
                  type="text"
                  placeholder="Enter your name"
                  style={{
                    width: "100%",
                    padding: "8px 12px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                    fontSize: "14px",
                  }}
                />
              </div>
              <div>
                <label style={{ display: "block", marginBottom: "4px", fontSize: "14px", fontWeight: "500" }}>
                  Email
                </label>
                <input
                  type="email"
                  placeholder="Enter your email"
                  style={{
                    width: "100%",
                    padding: "8px 12px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                    fontSize: "14px",
                  }}
                />
              </div>
            </div>
          </BottomSheetContent>
        </BottomSheet>
      </>
    )
  },
}

/** BottomSheet with action list */
export const WithActions: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheet displaying a list of actions, commonly used for sharing, settings, or quick actions.",
      },
    },
  },
  render: function WithActionsDemo() {
    const [open, setOpen] = useState(false)

    const actions = [
      { icon: <Share size={20} />, label: "Share", description: "Share this item", id: "share" },
      { icon: <Heart size={20} />, label: "Add to Favorites", description: "Save to your favorites", id: "favorite" },
      { icon: <Settings size={20} />, label: "Settings", description: "Manage preferences", id: "settings" },
    ]

    return (
      <>
        <div style={{ padding: "20px" }}>
          <Button onClick={() => setOpen(true)}>Open Actions Bottom Sheet</Button>
        </div>
        <BottomSheet
          open={open}
          onClose={() => setOpen(false)}
          title="Quick Actions"
        >
          <div style={{ padding: "8px 0" }}>
            {actions.map((action) => (
              <button
                key={action.id}
                onClick={() => setOpen(false)}
                style={{
                  width: "100%",
                  display: "flex",
                  alignItems: "center",
                  gap: "12px",
                  padding: "12px 16px",
                  border: "none",
                  background: "transparent",
                  cursor: "pointer",
                  fontSize: "14px",
                  textAlign: "left",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#f5f5f5"
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent"
                }}
              >
                <div style={{ color: "#666" }}>{action.icon}</div>
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: "500", marginBottom: "2px" }}>
                    {action.label}
                  </div>
                  <div style={{ fontSize: "12px", color: "#666" }}>
                    {action.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </BottomSheet>
      </>
    )
  },
}

/** BottomSheet without title */
export const NoTitle: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheet without a title, showing just the drag handle and content.",
      },
    },
  },
  render: function NoTitleDemo() {
    const [open, setOpen] = useState(false)

    return (
      <>
        <div style={{ padding: "20px" }}>
          <Button onClick={() => setOpen(true)}>Open Bottom Sheet (No Title)</Button>
        </div>
        <BottomSheet
          open={open}
          onClose={() => setOpen(false)}
        >
          <div style={{ padding: "16px" }}>
            <Typography level="titleMedium" style={{ marginBottom: "8px" }}>
              Custom Content Header
            </Typography>
            <Typography level="bodyMedium">
              This bottom sheet doesn't have a title in the header, allowing for more flexible content layout.
            </Typography>
          </div>
        </BottomSheet>
      </>
    )
  },
}

/** BottomSheet with custom close icon */
export const CustomCloseIcon: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheet with a custom close icon instead of the default close button.",
      },
    },
  },
  render: function CustomCloseIconDemo() {
    const [open, setOpen] = useState(false)

    return (
      <>
        <div style={{ padding: "20px" }}>
          <Button onClick={() => setOpen(true)}>Open with Custom Close Icon</Button>
        </div>
        <BottomSheet
          open={open}
          onClose={() => setOpen(false)}
          title="Custom Close"
          closeIcon={
            <button
              onClick={() => setOpen(false)}
              style={{
                background: "#ff4757",
                color: "white",
                border: "none",
                borderRadius: "50%",
                width: "32px",
                height: "32px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                fontSize: "16px",
              }}
            >
              ×
            </button>
          }
        >
          <div style={{ padding: "16px" }}>
            <Typography level="bodyLarge">
              This bottom sheet uses a custom close icon with different styling.
            </Typography>
          </div>
        </BottomSheet>
      </>
    )
  },
}

/** Comprehensive showcase of BottomSheet configurations */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of different BottomSheet configurations and use cases.",
      },
    },
  },
  render: function StatesDemo() {
    const [openBasic, setOpenBasic] = useState(false)
    const [openForm, setOpenForm] = useState(false)
    const [openActions, setOpenActions] = useState(false)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px", padding: "20px" }}>
        <Typography level="headlineSmall" style={{ marginBottom: "16px" }}>
          BottomSheet Examples
        </Typography>

        <div style={{ display: "flex", gap: "12px", flexWrap: "wrap" }}>
          <Button onClick={() => setOpenBasic(true)}>Basic Content</Button>
          <Button onClick={() => setOpenForm(true)} variant="outline">Form Example</Button>
          <Button onClick={() => setOpenActions(true)} variant="text">Action List</Button>
        </div>

        {/* Basic BottomSheet */}
        <BottomSheet
          open={openBasic}
          onClose={() => setOpenBasic(false)}
          title="Basic Information"
        >
          <div style={{ padding: "16px" }}>
            <Typography level="bodyLarge">
              This is a basic bottom sheet with simple content and a title.
            </Typography>
          </div>
        </BottomSheet>

        {/* Form BottomSheet */}
        <BottomSheet
          open={openForm}
          onClose={() => setOpenForm(false)}
          title="Quick Form"
        >
          <BottomSheetContent
            footer={
              <div style={{ display: "flex", gap: "8px" }}>
                <Button variant="outline" onClick={() => setOpenForm(false)} style={{ flex: 1 }}>
                  Cancel
                </Button>
                <Button onClick={() => setOpenForm(false)} style={{ flex: 1 }}>
                  Submit
                </Button>
              </div>
            }
          >
            <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
              <input
                type="text"
                placeholder="Enter text..."
                style={{
                  padding: "8px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              />
              <textarea
                placeholder="Enter description..."
                rows={3}
                style={{
                  padding: "8px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                  resize: "vertical",
                }}
              />
            </div>
          </BottomSheetContent>
        </BottomSheet>

        {/* Actions BottomSheet */}
        <BottomSheet
          open={openActions}
          onClose={() => setOpenActions(false)}
          title="Actions"
        >
          <div style={{ padding: "8px 0" }}>
            {[
              { icon: <Share size={20} />, label: "Share", id: "share" },
              { icon: <Heart size={20} />, label: "Favorite", id: "favorite" },
              { icon: <Settings size={20} />, label: "Settings", id: "settings" },
            ].map((action) => (
              <button
                key={action.id}
                onClick={() => setOpenActions(false)}
                style={{
                  width: "100%",
                  display: "flex",
                  alignItems: "center",
                  gap: "12px",
                  padding: "12px 16px",
                  border: "none",
                  background: "transparent",
                  cursor: "pointer",
                  fontSize: "14px",
                  textAlign: "left",
                }}
              >
                <div style={{ color: "#666" }}>{action.icon}</div>
                <div>{action.label}</div>
              </button>
            ))}
          </div>
        </BottomSheet>
      </div>
    )
  },
}
