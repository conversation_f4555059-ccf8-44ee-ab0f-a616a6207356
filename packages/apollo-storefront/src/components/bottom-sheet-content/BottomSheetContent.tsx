import classNames from "classnames";
import { BottomSheetContentProps } from "./BottomSheetContentProps";

import styles from "./bottom-sheet-content.module.css";

export function BottomSheetContent({
  children,
  footer,
  className,
  ...props
}: BottomSheetContentProps) {
    return (
      <div
        className={classNames(
          "ApolloBottomSheetContent-root",
          styles.bottomSheetContentRoot,
          className
        )}
        {...props}
      >
        {children}
        {footer && (
          <div
            className={classNames(
              "ApolloBottomSheetContent-footer",
              styles.bottomSheetFooter
            )}
          >
            {footer}
          </div>
        )}
      </div>
    )
  }
