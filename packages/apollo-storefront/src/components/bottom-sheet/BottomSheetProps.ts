import { Modal } from "@apollo/ui"
import { ComponentProps, ReactNode } from "react"

export type SnapPointProps = {
  headerHeight: number
  footerHeight: number
  height: number
  minHeight: number
  maxHeight: number
}

export type snapPoints = (props: SnapPointProps) => number[] | number

export type BottomSheetProps = ComponentProps<typeof Modal.Root> & {
  snapPoints?: snapPoints
  title?: string
  closeIcon?: ReactNode
  onClose?: () => void
}