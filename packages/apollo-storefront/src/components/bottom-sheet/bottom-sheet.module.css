.bottomSheetRoot {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: stretch;
    width: 100%;
    height: 100%;
}

.bottomSheetContainer {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    width: 100%;
    height: 100%;
    border-radius: var(--apl-alias-radius-radius8, 16px) var(--apl-alias-radius-radius8, 16px) var(--apl-alias-radius-radius1, 0) var(--apl-alias-radius-radius1, 0);
    background: var(--apl-alias-color-background-and-surface-background, #FFF);
    box-shadow: var(--apl-alias-elevation-elevations1-x-axis, 0) var(--apl-alias-elevation-elevations1-y-axis, 2px) var(--apl-alias-elevation-elevations1-blur, 4px) var(--apl-alias-elevation-elevations1-spread, 0) var(--apl-alias-elevation-elevations1-color, rgba(0, 0, 0, 0.10));
}

.bottomSheetDragger {
    width: 68px;
    height: 2px;
    background-color: var(--apl-alias-color-outline-and-border-Outline-Variant, #C8C6C6);
    cursor: grab;
}

.bottomSheetHeader {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: var(--apl-alias-spacing-padding-padding5, 8px) var(--apl-alias-spacing-padding-padding8, 16px);
}

.bottomSheetContent {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    flex: 1 0 0;
    padding: var(--apl-alias-spacing-padding-padding1, 0) var(--apl-alias-spacing-padding-padding8, 16px) var(--apl-alias-spacing-padding-padding8, 16px) var(--apl-alias-spacing-padding-padding8, 16px);
}