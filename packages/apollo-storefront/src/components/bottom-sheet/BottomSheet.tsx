import { Icon<PERSON>utton, Modal, Typography } from "@apollo/ui"
import classNames from "classnames"

import { Close } from "../common/Close"
import styles from "./bottom-sheet.module.css"
import { BottomSheetProps } from "./BottomSheetProps"

export function BottomSheet({
  className,
  open,
  onClose,
  snapPoints,
  title,
  closeIcon,
  children,
  ...props
}: BottomSheetProps) {
  return (
    <Modal.Root
      className={classNames(
        "ApolloBottomSheet-root",
        "ApolloBottomSheet-dialog",
        styles.bottomSheetRoot,
        className
      )}
      open={open}
      {...props}
    >
      <div
        className={classNames(
          "ApolloBottomSheet-container",
          styles.bottomSheetContainer
        )}
      >
        <div
          className={classNames(
            "ApolloBottomSheet-dragger",
            styles.bottomSheetDragger
          )}
        />
        <div
          className={classNames(
            "ApolloBottomSheet-header",
            styles.bottomSheetHeader
          )}
        >
          {title && <Typography level="titleMedium">{title}</Typography>}
          {closeIcon
            ? closeIcon
            : onClose && (
                <IconButton onClick={onClose}>
                  <Close />
                </IconButton>
              )}
        </div>
        <div
          className={classNames(
            "ApolloBottomSheet-content",
            styles.bottomSheetContent
          )}
        >
          {children}
        </div>
      </div>
    </Modal.Root>
  )
}
